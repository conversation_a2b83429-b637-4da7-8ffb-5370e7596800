import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { User, Mail, Save, AlertCircle, CheckCircle } from "lucide-react";
import { motion } from "framer-motion";
import toast from "react-hot-toast";

import { useAuth } from "@/hooks/useAuth";
import { useUpdateUserInfo } from "@/hooks/useUserManagement";
import {
  updateUserInfoSchema,
  UpdateUserInfoFormValues,
} from "@/schemas/userManagement";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import Modal from "@/components/ui/Modal";

const UserProfile: React.FC = () => {
  const { user, isLoading: isUserLoading } = useAuth();
  const updateUserInfoMutation = useUpdateUserInfo();
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [pendingData, setPendingData] =
    useState<UpdateUserInfoFormValues | null>(null);

  // Form setup with Zod validation
  const {
    register,
    handleSubmit,
    formState: { errors, isDirty },
    reset,
    watch,
  } = useForm<UpdateUserInfoFormValues>({
    resolver: zodResolver(updateUserInfoSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
    },
  });

  // Watch form values for real-time validation feedback
  const watchedValues = watch();

  // Pre-populate form with current user data
  useEffect(() => {
    if (user) {
      reset({
        first_name: user.first_name || "",
        last_name: user.last_name || "",
        email: user.email || "",
      });
    }
  }, [user, reset]);

  // Handle form submission with confirmation
  const onSubmit = (data: UpdateUserInfoFormValues) => {
    setPendingData(data);
    setShowConfirmModal(true);
  };

  // Handle confirmed update
  const handleConfirmedUpdate = async () => {
    if (!pendingData) return;

    try {
      await updateUserInfoMutation.mutateAsync(pendingData);

      // Show success toast
      toast.success("Profile updated successfully");

      // Reset form dirty state
      reset(pendingData);

      // Close modal
      setShowConfirmModal(false);
      setPendingData(null);
    } catch (error) {
      // Show error toast
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to update profile. Please try again."
      );
    }
  };

  // Handle keyboard navigation in modal
  const handleModalKeyDown = (e: React.KeyboardEvent) => {
    if (updateUserInfoMutation.isPending) return;

    if (e.key === "Enter" && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleConfirmedUpdate();
    }
  };

  // Handle modal close
  const handleModalClose = () => {
    if (updateUserInfoMutation.isPending) return; // Prevent closing during update
    setShowConfirmModal(false);
    setPendingData(null);
  };

  // Check if there are any changes
  const hasChanges =
    isDirty &&
    user &&
    (watchedValues.first_name !== (user.first_name || "") ||
      watchedValues.last_name !== (user.last_name || "") ||
      watchedValues.email !== (user.email || ""));

  // Helper function to get change summary
  const getChangeSummary = () => {
    if (!pendingData || !user) return [];

    const changes = [];
    if (user.first_name !== pendingData.first_name) {
      changes.push("First Name");
    }
    if (user.last_name !== pendingData.last_name) {
      changes.push("Last Name");
    }
    if (user.email !== pendingData.email) {
      changes.push("Email");
    }
    return changes;
  };

  // Show loading state while user data is being fetched
  if (isUserLoading) {
    return (
      <div className="max-w-2xl mx-auto space-y-6">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-dark-100 mb-2">
            Profile Settings
          </h1>
          <p className="text-dark-400">
            Manage your personal information and account settings.
          </p>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="animate-pulse space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="h-10 bg-dark-800 rounded"></div>
                <div className="h-10 bg-dark-800 rounded"></div>
              </div>
              <div className="h-10 bg-dark-800 rounded"></div>
              <div className="h-10 bg-dark-800 rounded w-32"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-dark-100 mb-2">
          Profile Settings
        </h1>
        <p className="text-dark-400">
          Manage your personal information and account settings.
        </p>
      </div>

      {/* Profile Information Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User size={20} />
            Personal Information
          </CardTitle>
          <CardDescription>
            Update your personal details. Changes will be reflected across your
            account.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Name Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="First Name"
                type="text"
                placeholder="Enter your first name"
                leftIcon={<User size={16} />}
                error={errors.first_name?.message}
                fullWidth
                {...register("first_name")}
              />

              <Input
                label="Last Name"
                type="text"
                placeholder="Enter your last name"
                leftIcon={<User size={16} />}
                error={errors.last_name?.message}
                fullWidth
                {...register("last_name")}
              />
            </div>

            {/* Email Field */}
            <Input
              label="Email Address"
              type="email"
              placeholder="Enter your email address"
              leftIcon={<Mail size={16} />}
              error={errors.email?.message}
              fullWidth
              {...register("email")}
            />

            {/* Action Buttons */}
            <div className="flex items-center justify-between pt-4 border-t border-dark-800">
              <div className="flex items-center gap-2 text-sm">
                {hasChanges && (
                  <motion.div
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="flex items-center gap-2 text-amber-400"
                  >
                    <AlertCircle size={16} />
                    <span>You have unsaved changes</span>
                  </motion.div>
                )}
              </div>

              <div className="flex items-center gap-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => reset()}
                  disabled={!hasChanges || updateUserInfoMutation.isPending}
                >
                  Reset
                </Button>

                <Button
                  type="submit"
                  leftIcon={<Save size={16} />}
                  disabled={!hasChanges || updateUserInfoMutation.isPending}
                  isLoading={updateUserInfoMutation.isPending}
                >
                  Save Changes
                </Button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Account Information Card */}
      <Card>
        <CardHeader>
          <CardTitle>Account Information</CardTitle>
          <CardDescription>
            Read-only account details and workspace information.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-dark-300 mb-1">
                User ID
              </label>
              <div className="px-3 py-2 bg-dark-800 border border-dark-700 rounded-md text-dark-400">
                {user?.user_id || "N/A"}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-dark-300 mb-1">
                Workspace
              </label>
              <div className="px-3 py-2 bg-dark-800 border border-dark-700 rounded-md text-dark-400">
                {user?.workspace_name || "N/A"}
              </div>
            </div>
          </div>

          {user?.roles && user.roles.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-dark-300 mb-2">
                Roles
              </label>
              <div className="flex flex-wrap gap-2">
                {user.roles.map((role) => (
                  <span
                    key={role.id}
                    className="px-2 py-1 bg-primary-500/10 text-primary-400 text-xs rounded-md border border-primary-500/20"
                  >
                    {role.name}
                  </span>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Enhanced Confirmation Modal */}
      <Modal
        isOpen={showConfirmModal}
        onClose={handleModalClose}
        title="Confirm Profile Update"
        size="lg"
        className="max-w-lg"
        showCloseButton={!updateUserInfoMutation.isPending}
      >
        <div className="p-6" onKeyDown={handleModalKeyDown}>
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            {/* Header Section with Icon and Message */}
            <div className="flex items-start gap-4">
              <motion.div
                initial={{ scale: 0.8 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.1, duration: 0.3 }}
                className="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-primary-500/20 to-primary-600/20 rounded-xl flex items-center justify-center border border-primary-500/30"
              >
                <CheckCircle size={24} className="text-primary-400" />
              </motion.div>
              <div className="flex-1 pt-1">
                <h3 className="text-lg font-semibold text-dark-100 mb-2">
                  Review Your Changes
                </h3>
                <p className="text-dark-300 text-sm leading-relaxed">
                  Please review the changes below before updating your profile.
                  These changes will be reflected across your account
                  immediately.
                </p>
                {pendingData && (
                  <div className="flex items-center gap-2 mt-2">
                    <span className="text-xs text-dark-400">
                      {getChangeSummary().length} field
                      {getChangeSummary().length !== 1 ? "s" : ""} will be
                      updated:
                    </span>
                    <span className="text-xs text-primary-400 font-medium">
                      {getChangeSummary().join(", ")}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Changes Summary */}
            {pendingData && user && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2, duration: 0.3 }}
                className="bg-gradient-to-br from-dark-800/80 to-dark-900/80 rounded-xl p-4 border border-dark-700/50 backdrop-blur-sm"
              >
                <h4 className="text-sm font-medium text-dark-200 mb-3 flex items-center gap-2">
                  <User size={16} className="text-primary-400" />
                  Profile Changes
                </h4>
                <div className="space-y-3">
                  {/* First Name Change */}
                  <div className="flex items-center justify-between py-2 px-3 bg-dark-800/50 rounded-lg border border-dark-700/30">
                    <span className="text-sm text-dark-400 font-medium">
                      First Name
                    </span>
                    <div className="flex items-center gap-2">
                      {user.first_name !== pendingData.first_name && (
                        <>
                          <span className="text-xs text-dark-500 line-through">
                            {user.first_name || "Not set"}
                          </span>
                          <span className="text-xs text-dark-400">→</span>
                        </>
                      )}
                      <span className="text-sm text-dark-100 font-medium">
                        {pendingData.first_name}
                      </span>
                    </div>
                  </div>

                  {/* Last Name Change */}
                  <div className="flex items-center justify-between py-2 px-3 bg-dark-800/50 rounded-lg border border-dark-700/30">
                    <span className="text-sm text-dark-400 font-medium">
                      Last Name
                    </span>
                    <div className="flex items-center gap-2">
                      {user.last_name !== pendingData.last_name && (
                        <>
                          <span className="text-xs text-dark-500 line-through">
                            {user.last_name || "Not set"}
                          </span>
                          <span className="text-xs text-dark-400">→</span>
                        </>
                      )}
                      <span className="text-sm text-dark-100 font-medium">
                        {pendingData.last_name}
                      </span>
                    </div>
                  </div>

                  {/* Email Change */}
                  <div className="flex items-center justify-between py-2 px-3 bg-dark-800/50 rounded-lg border border-dark-700/30">
                    <span className="text-sm text-dark-400 font-medium">
                      Email
                    </span>
                    <div className="flex items-center gap-2">
                      {user.email !== pendingData.email && (
                        <>
                          <span className="text-xs text-dark-500 line-through">
                            {user.email || "Not set"}
                          </span>
                          <span className="text-xs text-dark-400">→</span>
                        </>
                      )}
                      <span className="text-sm text-dark-100 font-medium">
                        {pendingData.email}
                      </span>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Action Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.3 }}
              className="flex items-center justify-end gap-3 pt-2"
            >
              <Button
                variant="ghost"
                size="md"
                onClick={handleModalClose}
                disabled={updateUserInfoMutation.isPending}
                className="min-w-[100px] hover:bg-dark-700/50 focus:ring-2 focus:ring-dark-500 focus:ring-offset-2 focus:ring-offset-dark-800"
                aria-label="Cancel profile update"
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                size="md"
                onClick={handleConfirmedUpdate}
                isLoading={updateUserInfoMutation.isPending}
                leftIcon={
                  !updateUserInfoMutation.isPending ? (
                    <Save size={16} />
                  ) : undefined
                }
                className="min-w-[140px] bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 shadow-lg shadow-primary-500/25 focus:ring-2 focus:ring-primary-400 focus:ring-offset-2 focus:ring-offset-dark-800"
                disabled={updateUserInfoMutation.isPending}
                aria-label={
                  updateUserInfoMutation.isPending
                    ? "Updating profile..."
                    : "Confirm and update profile"
                }
                autoFocus
              >
                {updateUserInfoMutation.isPending
                  ? "Updating..."
                  : "Update Profile"}
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </Modal>
    </div>
  );
};

export default UserProfile;
