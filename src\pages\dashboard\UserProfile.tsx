import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { User, Mail, Save, AlertCircle, CheckCircle } from "lucide-react";
import { motion } from "framer-motion";
import toast from "react-hot-toast";

import { useAuth } from "@/hooks/useAuth";
import { useUpdateUserInfo } from "@/hooks/useUserManagement";
import {
  updateUserInfoSchema,
  UpdateUserInfoFormValues,
} from "@/schemas/userManagement";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import Modal from "@/components/ui/Modal";

const UserProfile: React.FC = () => {
  const { user, isLoading: isUserLoading } = useAuth();
  const updateUserInfoMutation = useUpdateUserInfo();
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [pendingData, setPendingData] =
    useState<UpdateUserInfoFormValues | null>(null);

  // Form setup with Zod validation
  const {
    register,
    handleSubmit,
    formState: { errors, isDirty },
    reset,
    watch,
  } = useForm<UpdateUserInfoFormValues>({
    resolver: zodResolver(updateUserInfoSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
    },
  });

  // Watch form values for real-time validation feedback
  const watchedValues = watch();

  // Pre-populate form with current user data
  useEffect(() => {
    if (user) {
      reset({
        first_name: user.first_name || "",
        last_name: user.last_name || "",
        email: user.email || "",
      });
    }
  }, [user, reset]);

  // Handle form submission with confirmation
  const onSubmit = (data: UpdateUserInfoFormValues) => {
    setPendingData(data);
    setShowConfirmModal(true);
  };

  // Handle confirmed update
  const handleConfirmedUpdate = async () => {
    if (!pendingData) return;

    try {
      await updateUserInfoMutation.mutateAsync(pendingData);

      // Show success toast
      toast.success("Profile updated successfully");

      // Reset form dirty state
      reset(pendingData);

      // Close modal
      setShowConfirmModal(false);
      setPendingData(null);
    } catch (error) {
      // Show error toast
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to update profile. Please try again."
      );
    }
  };

  // Handle modal close
  const handleModalClose = () => {
    setShowConfirmModal(false);
    setPendingData(null);
  };

  // Check if there are any changes
  const hasChanges =
    isDirty &&
    user &&
    (watchedValues.first_name !== (user.first_name || "") ||
      watchedValues.last_name !== (user.last_name || "") ||
      watchedValues.email !== (user.email || ""));

  // Show loading state while user data is being fetched
  if (isUserLoading) {
    return (
      <div className="max-w-2xl mx-auto space-y-6">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-dark-100 mb-2">
            Profile Settings
          </h1>
          <p className="text-dark-400">
            Manage your personal information and account settings.
          </p>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="animate-pulse space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="h-10 bg-dark-800 rounded"></div>
                <div className="h-10 bg-dark-800 rounded"></div>
              </div>
              <div className="h-10 bg-dark-800 rounded"></div>
              <div className="h-10 bg-dark-800 rounded w-32"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-dark-100 mb-2">
          Profile Settings
        </h1>
        <p className="text-dark-400">
          Manage your personal information and account settings.
        </p>
      </div>

      {/* Profile Information Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User size={20} />
            Personal Information
          </CardTitle>
          <CardDescription>
            Update your personal details. Changes will be reflected across your
            account.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Name Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="First Name"
                type="text"
                placeholder="Enter your first name"
                leftIcon={<User size={16} />}
                error={errors.first_name?.message}
                fullWidth
                {...register("first_name")}
              />

              <Input
                label="Last Name"
                type="text"
                placeholder="Enter your last name"
                leftIcon={<User size={16} />}
                error={errors.last_name?.message}
                fullWidth
                {...register("last_name")}
              />
            </div>

            {/* Email Field */}
            <Input
              label="Email Address"
              type="email"
              placeholder="Enter your email address"
              leftIcon={<Mail size={16} />}
              error={errors.email?.message}
              fullWidth
              {...register("email")}
            />

            {/* Action Buttons */}
            <div className="flex items-center justify-between pt-4 border-t border-dark-800">
              <div className="flex items-center gap-2 text-sm">
                {hasChanges && (
                  <motion.div
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="flex items-center gap-2 text-amber-400"
                  >
                    <AlertCircle size={16} />
                    <span>You have unsaved changes</span>
                  </motion.div>
                )}
              </div>

              <div className="flex items-center gap-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => reset()}
                  disabled={!hasChanges || updateUserInfoMutation.isPending}
                >
                  Reset
                </Button>

                <Button
                  type="submit"
                  leftIcon={<Save size={16} />}
                  disabled={!hasChanges || updateUserInfoMutation.isPending}
                  isLoading={updateUserInfoMutation.isPending}
                >
                  Save Changes
                </Button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Account Information Card */}
      <Card>
        <CardHeader>
          <CardTitle>Account Information</CardTitle>
          <CardDescription>
            Read-only account details and workspace information.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-dark-300 mb-1">
                User ID
              </label>
              <div className="px-3 py-2 bg-dark-800 border border-dark-700 rounded-md text-dark-400">
                {user?.user_id || "N/A"}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-dark-300 mb-1">
                Workspace
              </label>
              <div className="px-3 py-2 bg-dark-800 border border-dark-700 rounded-md text-dark-400">
                {user?.workspace_name || "N/A"}
              </div>
            </div>
          </div>

          {user?.roles && user.roles.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-dark-300 mb-2">
                Roles
              </label>
              <div className="flex flex-wrap gap-2">
                {user.roles.map((role) => (
                  <span
                    key={role.id}
                    className="px-2 py-1 bg-primary-500/10 text-primary-400 text-xs rounded-md border border-primary-500/20"
                  >
                    {role.name}
                  </span>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Confirmation Modal */}
      <Modal
        isOpen={showConfirmModal}
        onClose={handleModalClose}
        title="Confirm Profile Update"
        size="md"
      >
        <div className="space-y-4">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 w-10 h-10 bg-primary-500/10 rounded-full flex items-center justify-center">
              <CheckCircle size={20} className="text-primary-400" />
            </div>
            <div className="flex-1">
              <p className="text-dark-200 mb-3">
                Are you sure you want to update your profile information?
              </p>

              {pendingData && (
                <div className="bg-dark-800 rounded-md p-3 space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-dark-400">First Name:</span>
                    <span className="text-dark-200">
                      {pendingData.first_name}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-dark-400">Last Name:</span>
                    <span className="text-dark-200">
                      {pendingData.last_name}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-dark-400">Email:</span>
                    <span className="text-dark-200">{pendingData.email}</span>
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="flex items-center justify-end gap-3 pt-4 border-t border-dark-800">
            <Button
              variant="outline"
              onClick={handleModalClose}
              disabled={updateUserInfoMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              onClick={handleConfirmedUpdate}
              isLoading={updateUserInfoMutation.isPending}
              leftIcon={<Save size={16} />}
            >
              Update Profile
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default UserProfile;
