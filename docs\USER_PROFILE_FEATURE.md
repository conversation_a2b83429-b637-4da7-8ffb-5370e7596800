# User Profile Update Feature

## Overview

The User Profile Update feature allows authenticated users to update their personal information including first name, last name, and email address through a dedicated profile page.

## Implementation Details

### API Integration

- **Endpoint**: `PUT /api/update-user-info`
- **Request Payload**:
  ```json
  {
    "email": "string",
    "first_name": "string", 
    "last_name": "string"
  }
  ```
- **Success Response**:
  ```json
  {
    "ok": true,
    "status_code": 200,
    "message": "User information updated successfully"
  }
  ```

### React Query Implementation

- **Hook**: `useUpdateUserInfo()` in `src/hooks/useUserManagement.ts`
- **Features**:
  - Optimistic updates for immediate UI feedback
  - Proper error handling with rollback on failure
  - Cache invalidation for both user management and auth caches
  - Loading states and error boundaries

### Form Validation

- **Schema**: `updateUserInfoSchema` in `src/schemas/userManagement.ts`
- **Validation Rules**:
  - First name: Required, 1-50 characters
  - Last name: Required, 1-50 characters
  - Email: Required, valid email format
- **Real-time validation** with immediate feedback

### UI/UX Features

- **Profile Page**: `/dashboard/profile`
- **Navigation**: Added to sidebar, accessible to all authenticated users
- **Form Features**:
  - Pre-populated with current user data
  - Real-time change detection
  - Confirmation dialog for updates
  - Loading states during API calls
  - Success/error toast notifications
  - Reset functionality

### Components

1. **UserProfile.tsx**: Main profile page component
2. **Navigation**: Added profile link to DashboardLayout
3. **Routing**: Added profile route to App.tsx

### Key Features

- **Optimistic Updates**: Changes appear immediately in UI
- **Confirmation Dialog**: Prevents accidental updates
- **Loading States**: Skeleton loading while fetching user data
- **Error Handling**: Graceful error handling with user feedback
- **Responsive Design**: Works on desktop and mobile
- **Accessibility**: Proper ARIA labels and keyboard navigation

## Usage

1. Navigate to the profile page via the sidebar "Profile" link
2. Update desired fields (first name, last name, email)
3. Click "Save Changes" to open confirmation dialog
4. Confirm the update to save changes
5. Receive success/error feedback via toast notifications

## Technical Architecture

### Cache Management
- Updates both `['users']` and `['user']` query caches
- Implements optimistic updates with rollback on error
- Proper cache invalidation after successful updates

### Error Handling
- ServiceError integration for consistent error handling
- Toast notifications for user feedback
- Form validation with real-time feedback
- Graceful fallbacks for network errors

### Type Safety
- Full TypeScript integration
- Zod schema validation
- Proper type inference for form values
- Type-safe API request/response handling

## Files Modified/Created

### New Files
- `src/pages/dashboard/UserProfile.tsx` - Main profile component
- `docs/USER_PROFILE_FEATURE.md` - This documentation

### Modified Files
- `src/types/api.ts` - Added UpdateUserInfoRequest/Response types
- `src/services/user.ts` - Added updateUserInfo service function
- `src/hooks/useUserManagement.ts` - Added useUpdateUserInfo hook
- `src/schemas/userManagement.ts` - Added updateUserInfoSchema
- `src/App.tsx` - Added profile route
- `src/layouts/DashboardLayout.tsx` - Added profile navigation

## Testing

To test the feature:

1. Start the development server: `npm run dev`
2. Login to the application
3. Navigate to the Profile page via sidebar
4. Update profile information and verify:
   - Form validation works correctly
   - Confirmation dialog appears
   - Success/error notifications display
   - Changes persist after page refresh
   - Optimistic updates work as expected

## Future Enhancements

Potential improvements for future iterations:

1. **Password Change**: Add password update functionality to profile page
2. **Avatar Upload**: Allow users to upload profile pictures
3. **Preferences**: Add user preference settings
4. **Two-Factor Authentication**: Add 2FA setup/management
5. **Activity Log**: Show recent account activity
6. **Data Export**: Allow users to export their data
