import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router-dom';
import UserProfile from '../pages/dashboard/UserProfile';
import { useAuth } from '../hooks/useAuth';
import { useUpdateUserInfo } from '../hooks/useUserManagement';

// Mock the hooks
vi.mock('../hooks/useAuth');
vi.mock('../hooks/useUserManagement');

const mockUseAuth = vi.mocked(useAuth);
const mockUseUpdateUserInfo = vi.mocked(useUpdateUserInfo);

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('UserProfile Component', () => {
  const mockUser = {
    user_id: 1,
    email: '<EMAIL>',
    first_name: 'John',
    last_name: 'Doe',
    workspace_id: 1,
    workspace_name: 'Test Workspace',
    roles: [{ id: 1, name: 'Admin', permissions: [] }],
    custom_roles: [],
  };

  const mockUpdateMutation = {
    mutateAsync: vi.fn(),
    isPending: false,
    isError: false,
    error: null,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockUseAuth.mockReturnValue({
      user: mockUser,
      isLoading: false,
      login: {} as any,
      signup: {} as any,
      logout: {} as any,
      refreshToken: {} as any,
      isAuthenticated: true,
    });

    mockUseUpdateUserInfo.mockReturnValue(mockUpdateMutation as any);
  });

  it('renders profile form with user data', () => {
    render(
      <TestWrapper>
        <UserProfile />
      </TestWrapper>
    );

    expect(screen.getByDisplayValue('John')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Doe')).toBeInTheDocument();
    expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
  });

  it('shows loading state when user is loading', () => {
    mockUseAuth.mockReturnValue({
      user: null,
      isLoading: true,
      login: {} as any,
      signup: {} as any,
      logout: {} as any,
      refreshToken: {} as any,
      isAuthenticated: false,
    });

    render(
      <TestWrapper>
        <UserProfile />
      </TestWrapper>
    );

    expect(screen.getByText('Profile Settings')).toBeInTheDocument();
    expect(document.querySelector('.animate-pulse')).toBeInTheDocument();
  });

  it('enables save button when form has changes', async () => {
    render(
      <TestWrapper>
        <UserProfile />
      </TestWrapper>
    );

    const firstNameInput = screen.getByDisplayValue('John');
    
    // Initially, save button should be disabled
    expect(screen.getByRole('button', { name: /save changes/i })).toBeDisabled();

    // Change the first name
    fireEvent.change(firstNameInput, { target: { value: 'Jane' } });

    await waitFor(() => {
      expect(screen.getByRole('button', { name: /save changes/i })).not.toBeDisabled();
    });
  });

  it('shows confirmation modal when submitting form', async () => {
    render(
      <TestWrapper>
        <UserProfile />
      </TestWrapper>
    );

    const firstNameInput = screen.getByDisplayValue('John');
    const saveButton = screen.getByRole('button', { name: /save changes/i });

    // Change the first name
    fireEvent.change(firstNameInput, { target: { value: 'Jane' } });

    // Click save button
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(screen.getByText('Confirm Profile Update')).toBeInTheDocument();
    });
  });

  it('displays user account information', () => {
    render(
      <TestWrapper>
        <UserProfile />
      </TestWrapper>
    );

    expect(screen.getByText('1')).toBeInTheDocument(); // User ID
    expect(screen.getByText('Test Workspace')).toBeInTheDocument();
    expect(screen.getByText('Admin')).toBeInTheDocument(); // Role
  });

  it('handles form validation errors', async () => {
    render(
      <TestWrapper>
        <UserProfile />
      </TestWrapper>
    );

    const firstNameInput = screen.getByDisplayValue('John');
    
    // Clear the first name to trigger validation error
    fireEvent.change(firstNameInput, { target: { value: '' } });
    fireEvent.blur(firstNameInput);

    await waitFor(() => {
      expect(screen.getByText('First name is required')).toBeInTheDocument();
    });
  });
});
